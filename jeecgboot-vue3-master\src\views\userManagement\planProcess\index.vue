<template>
  <div>
    <!--引用表格-->
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <!--插槽:table标题-->
      <template #tableTitle>
        <!-- <a-button type="primary" @click="handleAdd" preIcon="ant-design:plus-outlined"> 新增</a-button>
        <a-button type="primary" preIcon="ant-design:export-outlined" @click="onExportXls"> 导出</a-button>
        <j-upload-button type="primary" preIcon="ant-design:import-outlined" @click="onImportXls">导入</j-upload-button> -->
        <!-- <a-dropdown v-if="selectedRowKeys.length > 0">
          <template #overlay>
            <a-menu>
              <a-menu-item key="1" @click="batchHandleDelete">
                <Icon icon="ant-design:delete-outlined"></Icon>
                删除
              </a-menu-item>
            </a-menu>
          </template>
          <a-button>批量操作
            <Icon icon="mdi:chevron-down"></Icon>
          </a-button>
        </a-dropdown> -->
      </template>
      <template #showProgress="{ record }">
        <div>
          
          <a-progress :percent="record.finishPercent ? record.finishPercent : 0" />
        </div>
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" />
      </template>
    </BasicTable>

    <!-- 表单区域 -->
    <PlanProcessModal @register="registerModal" @success="handleSuccess"></PlanProcessModal>
  </div>
</template>

<script lang="ts" name="PlanDevelopment" setup>
import { ref, computed, unref } from 'vue';
import { BasicTable, useTable, TableAction } from '/@/components/Table';
import { useModal } from '/@/components/Modal';
import { useListPage } from '/@/hooks/system/useListPage';
import PlanProcessModal from './modules/PlanProcessModal.vue';
import { columns, searchFormSchema } from './PlanProcess.data';
import { list, deleteOne, batchDelete, getImportUrl, getExportUrl } from './PlanProcess.api';
import { message, Progress } from 'ant-design-vue';
import dayjs from 'dayjs';

const AProgress = Progress;
//注册model
const [registerModal, { openModal }] = useModal();
//注册table数据
const { prefixCls, tableContext, onExportXls, onImportXls } = useListPage({
  tableProps: {
    title: '人员管理-培训考核计划处理',
    api: list,
    columns,
    canResize: false,
    formConfig: {
      labelWidth: 120,
      schemas: searchFormSchema,
      autoSubmitOnEnter: true,
      showAdvancedButton: true,
      fieldMapToTime: [['timeRange', ['planStartTime', 'planEndTime'], 'YYYY-MM-DD']],
    },
    actionColumn: {
      width: 150,
    },
  },
  exportConfig: {
    name: '人员管理-培训考核计划处理',
    url: getExportUrl,
  },
  importConfig: {
    url: getImportUrl,
  },
});

const [registerTable, { reload }, { rowSelection, selectedRowKeys }] = tableContext;

/**
 * 新增事件
 */
function handleAdd() {
  openModal(true, {
    isUpdate: false,
    showFooter: true,
  });
}
/**
 * 编辑事件
 */
function handleEdit(record: Recordable) {
  console.log('start', dayjs(record.planStartTime).valueOf())
  console.log('dayjs', dayjs().valueOf())
  console.log('end', dayjs(record.planEndTime).valueOf())
  console.log('res', dayjs(record.planStartTime).valueOf() < dayjs().valueOf() && dayjs(record.planEndTime+' 23:59:59').valueOf() > dayjs().valueOf())
  if(dayjs(record.planStartTime).valueOf() < dayjs().valueOf()) {
    openModal(true, {
      record,
      isUpdate: true,
      showFooter: true,
    });
  } else {
    message.warn('小于计划开始时间，无法操作！')
  }
  
}
/**
 * 详情
 */
function handleDetail(record: Recordable) {
  openModal(true, {
    record,
    isUpdate: true,
    showFooter: false,
  });
}
/**
 * 删除事件
 */
async function handleDelete(record) {
  await deleteOne({ id: record.id }, reload);
}
/**
 * 批量删除事件
 */
async function batchHandleDelete() {
  await batchDelete({ ids: selectedRowKeys.value }, reload);
}
/**
 * 成功回调
 */
function handleSuccess({ isUpdate, values }) {
  reload();
}
/**
 * 操作栏
 */
function getTableAction(record) {
  return [
    {
      label: '计划处理',
      onClick: handleEdit.bind(null, record),
      ifShow: () => {
        
        return record.status == '1' ;
      },
    },
    // {
    //   label: '提交',
    //   popConfirm: {
    //     title: '是否确认提交',
    //     confirm: handleDelete.bind(null, record),
    //   },
    //   ifShow: () => {
    //     return record.status == '0';
    //   },
    // }
  ];
}
/**
 * 下拉操作栏
 */
function getDropDownAction(record) {
  return [
    // {
    //   label: '详情',
    //   onClick: handleDetail.bind(null, record),
    // },
    {
      label: '删除',
      popConfirm: {
        title: '是否确认删除',
        confirm: handleDelete.bind(null, record),
      },
      ifShow: () => {
        return record.status == '0';
      },
    },
    
  ];
}
</script>
<style scoped>
</style>
