<template>
  <div>
    <!--引用表格-->
    <BasicTable @register="registerTable">
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-button type="primary" @click="handleAdd" preIcon="ant-design:plus-outlined"> 新增</a-button>
        <!-- <a-button type="primary" preIcon="ant-design:export-outlined" @click="onExportXls"> 导出</a-button> -->
        <!-- <j-upload-button type="primary" preIcon="ant-design:import-outlined" @click="onImportXls">导入</j-upload-button> -->
        <!-- <a-dropdown v-if="selectedRowKeys.length > 0">
          <template #overlay>
            <a-menu>
              <a-menu-item key="1" @click="batchHandleDelete">
                <Icon icon="ant-design:delete-outlined"></Icon>
                删除
              </a-menu-item>
            </a-menu>
          </template>
<a-button>批量操作
  <Icon icon="mdi:chevron-down"></Icon>
</a-button>
</a-dropdown> -->
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" :dropDownActions="getDropDownAction(record)" />
      </template>
    </BasicTable>

    <!-- 表单区域 -->
    <dailySupervisionPlanModal @register="registerModal" @success="handleSuccess"></dailySupervisionPlanModal>
    <!-- 审批表打印 -->
    <dailySupervisionPlanTableModal @register="registerdailySupervisionPlanTableModal" @success="handleSuccess"></dailySupervisionPlanTableModal>

    <!-- 驳回理由弹窗 -->
    <a-modal v-model:visible="rejectModalVisible" :title="rejectModalTitle" @ok="handleRejectConfirm"
      @cancel="handleRejectCancel" width="500px">
      <a-form :label-col="{ span: 4 }" :wrapper-col="{ span: 18 }">
        <a-form-item label="驳回理由" :rules="[{ required: true, message: '请输入驳回理由' }]">
          <a-textarea v-model:value="rejectReason" placeholder="请输入驳回理由" :rows="4" :maxlength="200" show-count />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 上传证书弹窗 -->
    <a-modal v-model:visible="uploadModalVisible" title="上传证书" @ok="handleUploadConfirm" @cancel="handleUploadCancel"
      width="500px">
      <a-form :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <a-form-item label="证书文件" :rules="[{ required: true, message: '请上传证书文件' }]">
          <j-upload v-model:value="certificateUrl" :maxCount="1" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 意见填写弹窗 -->
    <a-modal v-model:visible="commentModalVisible" :title="commentModalTitle" @ok="handleCommentConfirm"
      @cancel="handleCommentCancel" width="500px">
      <a-form :label-col="{ span: 4 }" :wrapper-col="{ span: 18 }">
        <a-form-item label="意见内容">
          <a-textarea
            v-model:value="commentContent"
            placeholder="请输入意见内容（可选）"
            :rows="4"
            :maxlength="200"
            show-count />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script lang="ts" name="userManagement-dailySupervisionPlan-index" setup>
import { ref, computed, unref } from 'vue';
import { BasicTable, useTable, TableAction } from '/@/components/Table';
import { useModal } from '/@/components/Modal';
import { useListPage } from '/@/hooks/system/useListPage';
import dailySupervisionPlanModal from './modules/dailySupervisionPlanModal.vue';
import dailySupervisionPlanTableModal from './modules/dailySupervisionPlanTableModal.vue';
import { columns, searchFormSchema } from './dailySupervisionPlan.data';
import { list, deleteOne, batchDelete, getImportUrl, getExportUrl, returnOne, auditOrRollBack } from './dailySupervisionPlan.api';
import { defHttp } from '/@/utils/http/axios';
import { message } from 'ant-design-vue';
import JUpload from '/@/components/Form/src/jeecg/components/JUpload/JUpload.vue';

//注册model
const [registerModal, { openModal }] = useModal();
const [registerdailySupervisionPlanTableModal, { openModal: opendailySupervisionPlanTableModal }] = useModal();

// 驳回弹窗相关状态
const rejectModalVisible = ref(false);
const rejectModalTitle = ref('');
const rejectReason = ref('');
const currentRejectRecord = ref(null);
const rejectType = ref(''); // 'upAudit' 或 'audit'

// 上传证书弹窗相关状态
const uploadModalVisible = ref(false);
const certificateUrl = ref('');
const currentUploadRecord = ref(null);

// 意见填写弹窗相关状态
const commentModalVisible = ref(false);
const commentModalTitle = ref('');
const commentContent = ref('');
const currentCommentRecord = ref(null);
const commentType = ref(''); // 'audit' 或 'upAudit'
//注册table数据
const { prefixCls, tableContext, onExportXls, onImportXls } = useListPage({
  tableProps: {
    title: '年度监察计划',
    api: list,
    columns,
    canResize: false,
    formConfig: {
      labelWidth: 120,
      schemas: searchFormSchema,
      autoSubmitOnEnter: true,
      showAdvancedButton: true,
      fieldMapToTime: [['timeRange', ['startTime', 'endTime'], 'YYYY-MM-DD']],
    },
    showActionColumn: true,
    showTableSetting: false,
    showIndexColumn: true,
    actionColumn: {
      width: 120,
    },
  },
  exportConfig: {
    name: '年度监察计划',
    url: getExportUrl,
  },
  importConfig: {
    url: getImportUrl,
  },
});

const [registerTable, { reload }, { rowSelection, selectedRowKeys }] = tableContext;

/**
 * 新增事件
 */
function handleAdd() {
  openModal(true, {
    isUpdate: false,
    showFooter: true,
  });
}
/**
 * 编辑事件
 */
function handleEdit(record: Recordable) {
  openModal(true, {
    record,
    isUpdate: true,
    showFooter: true,
  });
}
/**
 * 删除事件
 */
async function handleDelete(record) {
  await deleteOne({ id: record.id }, reload);
}
/**
 * 退回事件
 */
async function handleReturn(record) {
  await returnOne({ id: record.id }, reload);
}
/**
 * 人员外出培训审批表
 */
function handleSeeSPB(record) {
  opendailySupervisionPlanTableModal(true, {
    record,
    isUpdate: true,
    showFooter: false,
  });
}
/**
 * 批量删除事件
 */
async function batchHandleDelete() {
  await batchDelete({ ids: selectedRowKeys.value }, reload);
}
/**
 *  技术审核
 */
function handleAudit(record) {
  currentCommentRecord.value = record;
  commentType.value = 'audit';
  commentModalTitle.value = '技术审核意见';
  commentContent.value = '';
  commentModalVisible.value = true;
}
/**
 * 同意
 */
function handleUpAudit(record) {
  currentCommentRecord.value = record;
  commentType.value = 'upAudit';
  commentModalTitle.value = '同意意见';
  commentContent.value = '';
  commentModalVisible.value = true;
}
/**
 * 发起审批驳回
 */
function handleUpAuditReject(record) {
  currentRejectRecord.value = record;
  rejectType.value = 'upAudit';
  rejectModalTitle.value = '发起审批驳回';
  rejectReason.value = '';
  rejectModalVisible.value = true;
}

/**
 * 技术审核驳回
 */
function handleAuditReject(record) {
  currentRejectRecord.value = record;
  rejectType.value = 'audit';
  rejectModalTitle.value = '技术审核驳回';
  rejectReason.value = '';
  rejectModalVisible.value = true;
}

/**
 * 驳回确认
 */
async function handleRejectConfirm() {
  if (!rejectReason.value.trim()) {
    message.error('请输入驳回理由');
    return;
  }

  try {
    const params: any = {
      id: currentRejectRecord.value.id,
      auditStatus: 99, // 驳回状态
      rejectReason: rejectReason.value, // 驳回原因
    };

    await auditOrRollBack(params);
    message.success('驳回成功');
    rejectModalVisible.value = false;
    reload();
  } catch (error) {
    message.error('驳回失败');
  }
}

/**
 * 驳回取消
 */
function handleRejectCancel() {
  rejectModalVisible.value = false;
  rejectReason.value = '';
  currentRejectRecord.value = null;
}

/**
 * 上传证书
 */
function handleUpload(record) {
  currentUploadRecord.value = record;
  certificateUrl.value = '';
  uploadModalVisible.value = true;
}

/**
 * 上传证书确认
 */
async function handleUploadConfirm() {
  if (!certificateUrl.value) {
    message.error('请上传证书文件');
    return;
  }

  try {
    await defHttp.put({
      url: '/lims/employee/uploadOutTrainingData',
      data: {
        id: currentUploadRecord.value.id,
        certificateUrl: certificateUrl.value,
      },
    });

    message.success('证书上传成功');
    uploadModalVisible.value = false;
    reload();
  } catch (error) {
    message.error('证书上传失败');
  }
}

/**
 * 上传证书取消
 */
function handleUploadCancel() {
  uploadModalVisible.value = false;
  certificateUrl.value = '';
  currentUploadRecord.value = null;
}

/**
 * 意见确认
 */
async function handleCommentConfirm() {
  try {
    const params: any = {
      id: currentCommentRecord.value.id,
    };

    if (commentType.value === 'audit') {
      // 技术审核
      params.auditContent = commentContent.value || '';
      params.auditStatus = 1;
    } else if (commentType.value === 'upAudit') {
      // 同意
      params.assignContent  = commentContent.value || '';
      params.auditStatus = 2;
    }

    await auditOrRollBack(params);
    message.success('操作成功');
    commentModalVisible.value = false;
    reload();
  } catch (error) {
    message.error('操作失败');
  }
}
async function handleConfirm(record){
   await defHttp.post({
      url: '/lims/employee/dailyMonitorPlanCommit',
      params:{id:record.id},
    });
    reload();
}
/**
 * 意见取消
 */
function handleCommentCancel() {
  commentModalVisible.value = false;
  commentContent.value = '';
  currentCommentRecord.value = null;
}

/**
 * 成功回调
 */
function handleSuccess({ isUpdate, values }) {
  reload();
}
/**
 * 操作栏
 */
function getTableAction(record) {
  return [
    {
      label: '编辑',
      ifShow: () => {
        return record.auditStatus == null;
      },
      onClick: handleEdit.bind(null, record),
    },
  ];
}
/**
 * 下拉操作栏
 */
function getDropDownAction(record) {
  return [
    {
      label: '删除',
      popConfirm: {
        title: '是否确认删除',
        confirm: handleDelete.bind(null, record),
      },
      ifShow: () => {
        return record.status == '0';
      },
    },
    {
      label: '提交',
      onClick: handleConfirm.bind(null, record),
      ifShow: () => {
        return record.auditStatus == null || record.auditStatus == '' || record.auditStatus == 99;
      },
    },
    {
      label: '同意',
      ifShow: () => {
        return record.auditStatus == '1';
      },
      onClick: handleUpAudit.bind(null, record),
    },
    {
      label: '驳回',
      ifShow: () => {
        return record.auditStatus == '1';
      },
      onClick: handleUpAuditReject.bind(null, record),
    },
    {
      label: '技术审核',
      ifShow: () => {
        return record.auditStatus == '0';
      },
      onClick: handleAudit.bind(null, record),
    },
    {
      label: '驳回',
      ifShow: () => {
        return record.auditStatus == '0';
      },
      onClick: handleAuditReject.bind(null, record),
    },
    {
      label: '日常监督记录',
      onClick: handleSeeSPB.bind(null, record),
    },
    // {
    //   label: '上传证书',
    //   ifShow: () => {
    //     return record.auditStatus == '2' && record.certificateUrl == null;
    //   },
    //   onClick: handleUpload.bind(null, record),
    // },
  ];
}
</script>
<style scoped></style>
