<template>
  <BasicModal v-bind="$attrs" @register="registerModal" title="人员外出培训审批表" okText="确认" @ok="handleSubmit" :width="1400">
    <div class="table-container">
      <div class="table-header">
        <div class="table-actions">
          <button class="action-btn" @click="printTable">打印</button>
        </div>
      </div>
      <table :id="printId" border="1" cellspacing="0" cellpadding="5" style="width: 100%; border-collapse: collapse">
        <thead>
          <tr>
            <th style="text-align: center" colspan="4">人员外出培训审批表</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <th width="25%">申请人姓名</th>
            <td width="25%">{{ formData.applicantName }}</td>
            <th width="25%">岗位</th>
            <td width="25%">{{ formData.position }}</td>
          </tr>
          <tr>
            <th>培训名称</th>
            <td colspan="3">{{ formData.trainingName }}</td>
          </tr>
          <tr>
            <th>培训时间</th>
            <td colspan="3">{{ formData.trainingTime }}</td>
          </tr>
          <tr>
            <th>主办单位</th>
            <td colspan="3">{{ formData.organizer }}</td>
          </tr>
          <tr>
            <th>培训内容</th>
            <td colspan="3">{{ formData.trainReason }}</td>
          </tr>
          <tr style="height: 6rem;">
            <th>技术负责人<br>意见</th>
            <td colspan="3">
              <br>
              <br>
              <br>
              <a-row>
                <a-col :span="12">技术负责人:</a-col>
                <a-col :span="12">日期:</a-col>
              </a-row>
            </td>
          </tr>
          <tr style="height: 6rem;">
            <th>实验室经理<br>意见</th>
            <td colspan="3">
              <br>
              <br>
              <br>
              <a-row>
                <a-col :span="12">实验室经理:</a-col>
                <a-col :span="12">日期:</a-col>
              </a-row>
            </td>
          </tr>
          <tr style="height: 6rem;">
            <th>培训实施<br>情况记录</th>
            <td colspan="3">
              <br>
              <br>
              <br>
              <a-row>
                <a-col :span="12">参加日:</a-col>
                <a-col :span="12">日期:</a-col>
              </a-row>
            </td>
          </tr>
          <tr style="height: 6rem;">
            <th>实验室确认</th>
            <td colspan="3">
              <br>
              <br>
              <br>
              <a-row>
                <a-col :span="12">确认人:</a-col>
                <a-col :span="12">日期:</a-col>
              </a-row>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </BasicModal>
</template>
<script lang="ts" name="UserJLModal" setup>
import { ref, computed, unref, reactive } from 'vue';
import { BasicModal, useModalInner } from '/@/components/Modal';
import { message } from 'ant-design-vue';
import printJS from 'print-js';
import { buildUUID } from '/@/utils/uuid';

const printId = ref('');

// 声明Emits
const emit = defineEmits(['success', 'register']);

// 定义表单数据
const formData = reactive({
  applicantName: '',
  position: '',
  trainingName: '',
  trainingTime: '',
  organizer: '',
  trainPurpose: '',
  trainReason: '',

});

//表单赋值
const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
  console.log("🚀 ~ data:", data)
  printId.value = buildUUID().toString();

  if (data && data.record) {
    // 如果有传入数据，则赋值
    formData.applicantName = data.record.userName || '';
    formData.position = data.record.position || '';
    formData.trainingName = data.record.className || '';
    formData.trainingTime = data.record.startTime + '-' + data.record.endTime || '';
    formData.organizer = data.record.organizingUnit || '';
    formData.trainPurpose = data.record.trainPurpose || '';
    formData.trainReason = data.record.trainReason || '';

  }
});

// 打印表格
function printTable() {
  printJS({
    type: 'html',
    printable: printId.value,
    scanStyles: false,
  });
}

async function handleSubmit() {
  closeModal();
}
</script>

<style scoped>
.table-container {
  padding: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.table-title {
  text-align: center;
  font-weight: bold;
  font-size: 18px;
  margin: 0;
}

.table-actions {
  display: flex;
  gap: 10px;
}

.action-btn {
  padding: 5px 10px;
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}


.action-btn:disabled {
  background-color: #d9d9d9;
  cursor: not-allowed;
}

table {
  border: 1px solid #ccc;
  width: 100%;
}

th,
td {
  border: 1px solid #ccc;
  text-align: center;
  padding: 8px;
  height: 40px;
}

th {
  background-color: #f2f2f2;
  font-weight: bold;
}
</style>
